<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试项目选择功能</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; padding: 20px; }
        .test-container { max-width: 600px; margin: 0 auto; }
        .test-input { width: 100%; padding: 10px; font-size: 14px; border: 1px solid #ddd; border-radius: 8px; margin: 10px 0; }
        .project-list { display: flex; flex-wrap: wrap; gap: 10px; margin: 20px 0; }
        .project-item { padding: 8px 16px; background: #f0f0f0; border: 1px solid #ddd; border-radius: 6px; cursor: pointer; }
        .project-item.selected { background: #007bff; color: white; }
        .info { background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>项目选择功能测试</h1>
        
        <div class="info">
            <h3>测试说明：</h3>
            <ul>
                <li>选择一个项目时：显示"项目名称+沟通记录"，超过100px显示省略号</li>
                <li>选择多个项目时：显示"第一个项目名称+等X个项目沟通记录"</li>
                <li>点击项目可以选择/取消选择</li>
            </ul>
        </div>
        
        <input type="text" class="test-input" id="displayInput" placeholder="选择结果将显示在这里" readonly>
        
        <div class="project-list">
            <div class="project-item" data-name="这是一个非常长的项目名称用来测试截断功能">这是一个非常长的项目名称用来测试截断功能</div>
            <div class="project-item" data-name="短项目名">短项目名</div>
            <div class="project-item" data-name="中等长度的项目名称">中等长度的项目名称</div>
            <div class="project-item" data-name="另一个超级长的项目名称用于测试多选时的显示效果">另一个超级长的项目名称用于测试多选时的显示效果</div>
            <div class="project-item" data-name="普通项目">普通项目</div>
            <div class="project-item" data-name="最后一个项目">最后一个项目</div>
        </div>
        
        <div id="selectedInfo" class="info">
            <strong>当前选择：</strong><span id="selectedList">无</span>
        </div>
    </div>

    <script>
        class ProjectSelector {
            constructor() {
                this.selectedProjects = [];
                this.displayInput = document.getElementById('displayInput');
                this.selectedList = document.getElementById('selectedList');
                this.bindEvents();
            }

            bindEvents() {
                const projectItems = document.querySelectorAll('.project-item');
                projectItems.forEach(item => {
                    item.addEventListener('click', () => {
                        const projectName = item.dataset.name;
                        
                        if (item.classList.contains('selected')) {
                            // 取消选择
                            item.classList.remove('selected');
                            this.selectedProjects = this.selectedProjects.filter(p => p !== projectName);
                        } else {
                            // 选择项目
                            item.classList.add('selected');
                            this.selectedProjects.push(projectName);
                        }
                        
                        this.updateDisplay();
                    });
                });
            }zzz

            updateDisplay() {
                let displayText = '';
                
                if (this.selectedProjects.length > 0) {
                    if (this.selectedProjects.length === 1) {
                        // 只选择一个项目：显示项目名称+沟通记录，如果名称太长则显示省略号
                        const projectName = this.selectedProjects[0];
                        displayText = this.truncateProjectName(projectName) + '沟通记录';
                    } else {
                        // 选择多个项目：显示第一个项目名称+等X个项目沟通记录
                        const firstProjectName = this.selectedProjects[0];
                        const truncatedName = this.truncateProjectName(firstProjectName);
                        displayText = truncatedName + '等' + this.selectedProjects.length + '个项目沟通记录';
                    }
                }
                
                this.displayInput.value = displayText;
                this.selectedList.textContent = this.selectedProjects.length > 0 ? 
                    this.selectedProjects.join(', ') : '无';
            }

            // 截断项目名称，如果超过100px则显示省略号
            truncateProjectName(projectName) {
                // 创建一个临时元素来测量文本宽度
                const tempElement = document.createElement('span');
                tempElement.style.visibility = 'hidden';
                tempElement.style.position = 'absolute';
                tempElement.style.fontSize = '14px'; // 与输入框字体大小一致
                tempElement.style.fontFamily = getComputedStyle(this.displayInput).fontFamily;
                tempElement.textContent = projectName;
                document.body.appendChild(tempElement);
                
                const textWidth = tempElement.offsetWidth;
                document.body.removeChild(tempElement);
                
                // 如果文本宽度超过100px，则截断并添加省略号
                if (textWidth > 100) {
                    // 二分法找到合适的截断长度
                    let left = 0;
                    let right = projectName.length;
                    let result = projectName;
                    
                    while (left < right) {
                        const mid = Math.floor((left + right + 1) / 2);
                        const testText = projectName.substring(0, mid) + '...';
                        
                        tempElement.textContent = testText;
                        document.body.appendChild(tempElement);
                        const testWidth = tempElement.offsetWidth;
                        document.body.removeChild(tempElement);
                        
                        if (testWidth <= 100) {
                            left = mid;
                            result = testText;
                        } else {
                            right = mid - 1;
                        }
                    }
                    
                    return result;
                }
                
                return projectName;
            }
        }

        // 初始化
        new ProjectSelector();
    </script>
</body>
</html>
