<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Search Button Animation Test</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { 
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
      background: #f5f5f5; 
      min-height: 100vh; 
      padding: 100px 20px; 
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 40px;
    }
    
    .test-container {
      background: white;
      border-radius: 40px;
      box-shadow: 0 1px 2px rgba(0,0,0,0.08), 0 4px 12px rgba(0,0,0,0.05);
      padding: 20px;
      position: relative;
      width: 400px;
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .search-button { 
      background: linear-gradient(135deg, #ff385c, #e31c5f); 
      border: none; 
      border-radius: 50%; 
      width: 48px; 
      height: 48px; 
      cursor: pointer; 
      display: flex; 
      align-items: center; 
      justify-content: center; 
      transition: all 0.2s ease, width 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), border-radius 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94); 
      box-shadow: 0 2px 8px rgba(255, 56, 92, 0.3); 
      padding: 0; 
      overflow: hidden; 
    }
    
    .search-button:hover { 
      background: linear-gradient(135deg, #e31c5f, #d1185a); 
      transform: scale(1.05); 
      box-shadow: 0 4px 12px rgba(255, 56, 92, 0.4); 
    }
    
    .search-button.expanded { 
      width: 80px; 
      border-radius: 24px; 
      justify-content: flex-start; 
      padding-left: 12px; 
    }
    
    .search-icon { 
      width: 16px; 
      height: 16px; 
      fill: white; 
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      position: relative;
      z-index: 1;
    }
    
    /* 默认圆形状态：SVG绝对居中 */
    .search-button:not(.expanded) .search-icon {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      margin: 0;
    }
    
    /* 展开状态：SVG跟随左边布局 */
    .search-button.expanded .search-icon { 
      position: static;
      transform: none;
      margin-right: 8px; 
    }
    
    .search-button-text { 
      color: white; 
      font-size: 16px; 
      font-weight: 500; 
      white-space: nowrap; 
      opacity: 0; 
      transform: translateX(10px); 
      transition: opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), width 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      width: 0;
      overflow: hidden;
      flex-shrink: 0;
    }
    
    .search-button.expanded .search-button-text { 
      opacity: 1; 
      transform: translateX(0);
      width: auto;
      overflow: visible;
    }
    
    .controls {
      display: flex;
      gap: 20px;
    }
    
    .control-button {
      padding: 10px 20px;
      border: 1px solid #ddd;
      border-radius: 8px;
      background: white;
      cursor: pointer;
      font-size: 14px;
    }
    
    .control-button:hover {
      background: #f5f5f5;
    }
    
    .description {
      text-align: center;
      color: #666;
      max-width: 600px;
      line-height: 1.5;
    }
  </style>
</head>
<body>
  <div class="description">
    <h2>Search Button 动画优化测试</h2>
    <p>优化后的按钮在默认圆形状态时，SVG图标绝对居中显示，不受隐藏的span元素影响。<br>
    当按钮展开时，SVG图标平滑地移动到左侧，文字从右侧滑入。</p>
  </div>
  
  <div class="test-container">
    <button class="search-button" id="searchButton">
      <svg class="search-icon" viewBox="0 0 32 32">
        <path d="M13 24c6.075 0 11-4.925 11-11S19.075 2 13 2 2 6.925 2 13s4.925 11 11 11zm8-3l9 9-3 3-9-9v-2h2z"/>
      </svg>
      <span class="search-button-text">搜索</span>
    </button>
  </div>
  
  <div class="controls">
    <button class="control-button" onclick="toggleExpanded()">切换展开状态</button>
    <button class="control-button" onclick="resetButton()">重置为圆形</button>
    <button class="control-button" onclick="expandButton()">展开按钮</button>
  </div>

  <script>
    const searchButton = document.getElementById('searchButton');
    
    function toggleExpanded() {
      searchButton.classList.toggle('expanded');
    }
    
    function resetButton() {
      searchButton.classList.remove('expanded');
    }
    
    function expandButton() {
      searchButton.classList.add('expanded');
    }
    
    // 自动演示
    let autoDemo = true;
    function startAutoDemo() {
      if (!autoDemo) return;
      
      setTimeout(() => {
        expandButton();
        setTimeout(() => {
          resetButton();
          setTimeout(() => {
            startAutoDemo();
          }, 2000);
        }, 2000);
      }, 2000);
    }
    
    // 点击按钮停止自动演示
    searchButton.addEventListener('click', () => {
      autoDemo = false;
      toggleExpanded();
    });
    
    // 开始自动演示
    startAutoDemo();
  </script>
</body>
</html>
