<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试持久化和Clear Button功能</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; padding: 20px; }
        .test-container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .test-input { width: 100%; padding: 10px; font-size: 14px; border: 1px solid #ddd; border-radius: 8px; margin: 10px 0; position: relative; }
        .clear-btn { position: absolute; right: 10px; top: 50%; transform: translateY(-50%); background: #ff4444; color: white; border: none; border-radius: 50%; width: 20px; height: 20px; cursor: pointer; display: none; }
        .active-indicator { background: #007bff; color: white; padding: 2px 8px; border-radius: 4px; font-size: 12px; margin: 5px 0; }
        .tabs { display: flex; gap: 10px; margin: 10px 0; }
        .tab { padding: 8px 16px; background: #f0f0f0; border: 1px solid #ddd; border-radius: 6px; cursor: pointer; }
        .tab.active { background: #007bff; color: white; }
        .content { display: none; padding: 15px; background: #f9f9f9; border-radius: 6px; margin: 10px 0; }
        .content.active { display: block; }
        .project-item, .time-item { padding: 8px 12px; margin: 5px; background: white; border: 1px solid #ddd; border-radius: 4px; cursor: pointer; display: inline-block; }
        .project-item.selected, .time-item.selected { background: #007bff; color: white; }
        .info { background: #e7f3ff; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .status { background: #f0f8f0; padding: 10px; border-radius: 6px; margin: 10px 0; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>持久化和Clear Button功能测试</h1>
        
        <div class="info">
            <h3>测试说明：</h3>
            <ul>
                <li><strong>持久化测试</strong>：选择项目或时间选项后，关闭下拉菜单，再次打开应该显示对应的标签页和选中状态</li>
                <li><strong>Clear Button测试</strong>：只有在输入框激活（蓝色指示器显示）且有内容时才显示清除按钮</li>
                <li>点击输入框激活，点击外部区域取消激活</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>模拟搜索输入框</h3>
            <div style="position: relative;">
                <input type="text" class="test-input" id="testInput" placeholder="点击激活输入框" readonly>
                <button class="clear-btn" id="clearBtn">×</button>
            </div>
            <div id="activeStatus" class="active-indicator" style="display: none;">输入框已激活</div>
            
            <div id="dropdown" style="display: none; border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-top: 10px;">
                <div class="tabs">
                    <div class="tab active" data-tab="projects">WorkFeed</div>
                    <div class="tab" data-tab="timeline">最近沟通</div>
                </div>
                
                <div class="content active" id="projects-content">
                    <h4>选择项目：</h4>
                    <div class="project-item" data-name="长项目名称测试截断功能">长项目名称测试截断功能</div>
                    <div class="project-item" data-name="短项目">短项目</div>
                    <div class="project-item" data-name="中等长度项目名称">中等长度项目名称</div>
                    <div class="project-item" data-name="另一个长项目名称">另一个长项目名称</div>
                </div>
                
                <div class="content" id="timeline-content">
                    <h4>选择时间：</h4>
                    <div class="time-item" data-name="本周">本周</div>
                    <div class="time-item" data-name="上周">上周</div>
                    <div class="time-item" data-name="近两周">近两周</div>
                    <div class="time-item" data-name="2025年8月">2025年8月</div>
                </div>
            </div>
            
            <div class="status" id="status">
                状态：未激活<br>
                选中项目：无<br>
                选中时间：无<br>
                当前标签：projects
            </div>
        </div>
    </div>

    <script>
        class TestPersistence {
            constructor() {
                this.isActive = false;
                this.selectedProjects = [];
                this.selectedTimes = [];
                this.currentTab = 'projects';
                this.init();
            }

            init() {
                this.input = document.getElementById('testInput');
                this.clearBtn = document.getElementById('clearBtn');
                this.activeStatus = document.getElementById('activeStatus');
                this.dropdown = document.getElementById('dropdown');
                this.status = document.getElementById('status');
                
                this.bindEvents();
                this.updateStatus();
            }

            bindEvents() {
                // 输入框点击激活
                this.input.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.activate();
                });

                // 点击外部取消激活
                document.addEventListener('click', (e) => {
                    if (!e.target.closest('.test-section')) {
                        this.deactivate();
                    }
                });

                // 清除按钮
                this.clearBtn.addEventListener('click', () => {
                    this.clearAll();
                });

                // 标签页切换
                document.querySelectorAll('.tab').forEach(tab => {
                    tab.addEventListener('click', () => {
                        this.switchTab(tab.dataset.tab);
                    });
                });

                // 项目选择
                document.querySelectorAll('.project-item').forEach(item => {
                    item.addEventListener('click', () => {
                        this.toggleProject(item.dataset.name, item);
                    });
                });

                // 时间选择
                document.querySelectorAll('.time-item').forEach(item => {
                    item.addEventListener('click', () => {
                        this.toggleTime(item.dataset.name, item);
                    });
                });
            }

            activate() {
                this.isActive = true;
                this.activeStatus.style.display = 'block';
                this.dropdown.style.display = 'block';
                
                // 根据当前内容智能切换标签页
                this.determineActiveTab();
                this.restoreSelections();
                this.updateClearButton();
                this.updateStatus();
            }

            deactivate() {
                this.isActive = false;
                this.activeStatus.style.display = 'none';
                this.dropdown.style.display = 'none';
                this.updateClearButton();
                this.updateStatus();
            }

            determineActiveTab() {
                const inputValue = this.input.value.trim();
                
                if (this.selectedProjects.length > 0) {
                    this.currentTab = 'projects';
                } else if (this.selectedTimes.length > 0) {
                    this.currentTab = 'timeline';
                } else if (inputValue.includes('等') && inputValue.includes('个项目')) {
                    this.currentTab = 'projects';
                } else if (inputValue.includes('本周') || inputValue.includes('上周') || inputValue.includes('月')) {
                    this.currentTab = 'timeline';
                }
                
                this.switchTab(this.currentTab);
            }

            switchTab(tabName) {
                this.currentTab = tabName;
                
                // 更新标签页UI
                document.querySelectorAll('.tab').forEach(tab => {
                    tab.classList.toggle('active', tab.dataset.tab === tabName);
                });
                
                document.querySelectorAll('.content').forEach(content => {
                    content.classList.toggle('active', content.id === tabName + '-content');
                });
                
                this.updateStatus();
            }

            restoreSelections() {
                // 恢复项目选择状态
                document.querySelectorAll('.project-item').forEach(item => {
                    item.classList.toggle('selected', this.selectedProjects.includes(item.dataset.name));
                });
                
                // 恢复时间选择状态
                document.querySelectorAll('.time-item').forEach(item => {
                    item.classList.toggle('selected', this.selectedTimes.includes(item.dataset.name));
                });
            }

            toggleProject(name, element) {
                const index = this.selectedProjects.indexOf(name);
                if (index > -1) {
                    this.selectedProjects.splice(index, 1);
                    element.classList.remove('selected');
                } else {
                    this.selectedProjects.push(name);
                    element.classList.add('selected');
                }
                
                // 清除时间选择（互斥）
                this.selectedTimes = [];
                document.querySelectorAll('.time-item').forEach(item => {
                    item.classList.remove('selected');
                });
                
                this.updateInput();
                this.updateClearButton();
                this.updateStatus();
            }

            toggleTime(name, element) {
                const index = this.selectedTimes.indexOf(name);
                if (index > -1) {
                    this.selectedTimes.splice(index, 1);
                    element.classList.remove('selected');
                } else {
                    this.selectedTimes = [name]; // 时间选项单选
                    document.querySelectorAll('.time-item').forEach(item => {
                        item.classList.remove('selected');
                    });
                    element.classList.add('selected');
                }
                
                // 清除项目选择（互斥）
                this.selectedProjects = [];
                document.querySelectorAll('.project-item').forEach(item => {
                    item.classList.remove('selected');
                });
                
                this.updateInput();
                this.updateClearButton();
                this.updateStatus();
            }

            updateInput() {
                let displayText = '';
                
                if (this.selectedProjects.length > 0) {
                    if (this.selectedProjects.length === 1) {
                        displayText = this.selectedProjects[0] + '沟通记录';
                    } else {
                        displayText = this.selectedProjects[0] + '等' + this.selectedProjects.length + '个项目沟通记录';
                    }
                } else if (this.selectedTimes.length > 0) {
                    displayText = this.selectedTimes[0] + '沟通记录';
                }
                
                this.input.value = displayText;
            }

            updateClearButton() {
                const hasContent = this.input.value.trim();
                const shouldShow = hasContent && this.isActive;
                
                this.clearBtn.style.display = shouldShow ? 'block' : 'none';
            }

            clearAll() {
                this.selectedProjects = [];
                this.selectedTimes = [];
                this.input.value = '';
                
                document.querySelectorAll('.project-item, .time-item').forEach(item => {
                    item.classList.remove('selected');
                });
                
                this.updateClearButton();
                this.updateStatus();
            }

            updateStatus() {
                this.status.innerHTML = `
                    状态：${this.isActive ? '已激活' : '未激活'}<br>
                    选中项目：${this.selectedProjects.length > 0 ? this.selectedProjects.join(', ') : '无'}<br>
                    选中时间：${this.selectedTimes.length > 0 ? this.selectedTimes.join(', ') : '无'}<br>
                    当前标签：${this.currentTab}<br>
                    Clear Button：${this.clearBtn.style.display === 'block' ? '显示' : '隐藏'}
                `;
            }
        }

        // 初始化
        new TestPersistence();
    </script>
</body>
</html>
