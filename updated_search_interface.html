<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>集成版搜索框（含沟通记录时间选择）</title>
  <style>
      .months-container {
          display: flex;
          align-items: center;
          overflow: hidden;
          position: relative;
          width: 100%;
          max-width: 600px; /* 确保触发overflow */
          padding: 20px 40px; /* 为按钮预留空间 */
          margin: 0 auto;
      }
      .months-grid {
          display: flex;
          flex-direction: row-reverse; /* 最新月份在右侧 */
          justify-content: flex-end;
          overflow-x: hidden;
          scrollbar-width: none;
          -ms-overflow-style: none;
          transition: transform 0.4s cubic-bezier(0, 0, 0.2, 1); /* 前快后慢 */
          min-width: 100%; /* 确保grid宽度 */
      }
      .months-grid::-webkit-scrollbar {
          display: none;
      }
      .month-card {
          flex: 0 0 120px;
          cursor: pointer;
          user-select: none;
          padding: 10px;
          text-align: center;
          background: #f9f9f9;
          border-radius: 8px;
          margin-left: 10px; /* 卡片间距 */
      }
      .month-card.active {
          background: #e0e0e0;
      }
      .nav-button {
          background: #fff;
          border: 1px solid #ddd;
          border-radius: 50%;
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          z-index: 100;
          transition: opacity 0.3s ease;
      }
      .nav-button#prev-months {
          left: 5px;
      }
      .nav-button#next-months {
          right: 5px;
      }
      .nav-button.hidden {
          opacity: 0;
          pointer-events: none;
      }
      * { margin: 0; padding: 0; box-sizing: border-box; }
      body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #fff; min-height: 100vh; padding: 100px 20px; }
      .search-container { max-width: 850px; margin: 0 auto; position: relative; }
      .search-bar { background: white; border-radius: 40px; box-shadow: 0 1px 2px rgba(0,0,0,0.08), 0 4px 12px rgba(0,0,0,0.05); display: flex; align-items: center; transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94); border: 1px solid #ddd; position: relative; z-index: 1001; overflow: visible; }
      .search-bar.expanded { box-shadow: none; background: #ebebeb; }
      .active-indicator { position: absolute; background: white; border-radius: 40px; 
          box-shadow: rgba(0, 0, 0, 0.1) 0px 3px 12px 0px, rgba(0, 0, 0, 0.08) 0px 1px 2px 0px;
          z-index: 1; opacity: 0; 
      }
      .active-indicator.show { opacity: 1; transform: scale(1); }
      .search-item { flex: 1; padding: 16px 32px; line-height: 32px; cursor: pointer; position: relative; z-index: 2; color: #555; transition: all 0.3s ease; border-radius: 40px; }
      .search-item[data-type="guests"] { padding-right: 64px; }
      .search-item:not(.active):hover { background: rgba(0,0,0,0.05); }
      .search-item.active { color: #000; background: transparent; }
      .search-bar.hovering-item .search-separator { opacity: 0; }
      .search-placeholder { font-size: 14px; color: #717171; font-weight: 400; }
      .search-separator { width: 1px; height: 32px; background-color: #ddd; transition: opacity 0.3s ease; opacity: 1; }
      .search-bar.expanded .search-separator { opacity: 0; }
      .search-item.active .search-placeholder { color: #000; }
      .search-button { background: linear-gradient(135deg, #ff385c, #e31c5f); border: none; border-radius: 50%; width: 48px; height: 48px; margin: 7px; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.2s ease, width 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), border-radius 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94); box-shadow: 0 2px 8px rgba(255, 56, 92, 0.3); position: absolute; right: 0; z-index: 1002; padding: 0 16px; overflow: hidden; }
      .search-button:hover { background: linear-gradient(135deg, #e31c5f, #d1185a); transform: scale(1.05); box-shadow: 0 4px 12px rgba(255, 56, 92, 0.4); }
      .search-button.expanded { width: 80px; border-radius: 24px; justify-content: flex-start; padding-left: 12px; }
      .search-icon { width: 16px; height: 16px; fill: white; transition: margin-right 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94); }
      .search-button.expanded .search-icon { margin-right: 8px; }
      .search-button-text { color: white; font-size: 16px; font-weight: 500; white-space: nowrap; opacity: 0; transform: translateX(10px); transition: opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94); }
      .search-button.expanded .search-button-text { opacity: 1; transform: translateX(0); }
      .dropdown-container { position: absolute; top: calc(100% + 12px); background: white; border-radius: 32px; filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.14)); opacity: 0; visibility: hidden; transform: scale(0.5); z-index: 1000; overflow: hidden; border: 1px solid rgba(0,0,0,0.08); transform-origin: top center; }
      .dropdown-container:not(.show) {
          visibility: hidden;
      }
      .dropdown-container.show {
          opacity: 1;
          visibility: visible;
          transform: translateY(0) scale(1);
      }
      .dropdown-content {
          padding: 0;
          min-height: 200px;
          max-height: 350px; /* Constrain height */
          display: flex;
          flex-direction: column;
          position: relative;
          overflow: hidden; /* Hide any potential overflow from the container itself */
      }
      .dropdown-section { margin-bottom: 32px; }
      .dropdown-section:last-child { margin-bottom: 0; }
      .dropdown-title { font-size: 16px; font-weight: 600; color: #222; margin-bottom: 20px; }
      .location-item { padding: 16px 20px; border-radius: 12px; cursor: pointer; transition: all 0.2s ease; margin-bottom: 8px; border: 1px solid transparent; }
      .location-item:hover { background: linear-gradient(135deg, #f8f9ff, #f0f2ff); border-color: rgba(103, 126, 234, 0.2); transform: translateX(4px); }
      .location-name { font-size: 15px; font-weight: 500; color: #222; margin-bottom: 2px; }
      .location-desc { font-size: 13px; color: #717171; }
      .location-item:active { transform: translateX(4px) scale(0.98); }
      
      /* Custom Scrollbar Styles */
      .custom-scrollbar {
          position: absolute;
          top: 42px;
          bottom: 30px;
          right: 15px;
          width: 8px;
          background: rgba(0,0,0,0.05);
          border-radius: 4px;
          display: none;
      }

      .custom-scrollbar-thumb {
          position: absolute;
          width: 100%;
          background: rgba(0,0,0,0.2);
          border-radius: 4px;
          cursor: pointer;
      }

      .scrollable-content {
          padding: 42px 20px 30px;
          overflow-y: scroll;
          flex-grow: 1;
          scrollbar-width: none;
          -ms-overflow-style: none;
      }

      .scrollable-content::-webkit-scrollbar {
          display: none;
      }
      @media (max-width: 768px) { 
          .search-container { max-width: 100%; margin: 0 16px; } 
          .search-item { padding: 16px 32px; } 
          .search-item[data-type="guests"] { padding-right: 64px; } 
          .dropdown-content { padding: 24px; } 
      }
  </style>
  <script>
      const ANIMATION_CONFIG = {
          durationTransform: 0.4,
          durationOpacity: 0.5,
          easing: 'cubic-bezier(0, 0, 0.2, 1)', /* 更新为前快后慢 */
          startDelay: 0
      };
      function getTransitionString() {
          const t = ANIMATION_CONFIG;
          return `transform ${t.durationTransform}s ${t.easing} ${t.startDelay}s, 
                  opacity ${t.durationOpacity}s ${t.easing} ${t.startDelay}s, 
                  left ${t.durationTransform}s ${t.easing} ${t.startDelay}s, 
                  top ${t.durationTransform}s ${t.easing} ${t.startDelay}s, 
                  width ${t.durationTransform}s ${t.easing} ${t.startDelay}s, 
                  height ${t.durationTransform}s ${t.easing} ${t.startDelay}s`;
      }
  </script>
    <link rel="stylesheet" href="time-picker.css">
</head>
<body>
  <div class="search-container">
      <div class="search-bar" id="searchBar">
          <div class="active-indicator" id="activeIndicator"></div>
          <div class="search-item" data-type="location">
              <div class="search-placeholder" id="locationText">电话号码或关键字搜索</div>
          </div>
          <div class="search-separator"></div>
          <div class="search-item" data-type="guests">
              <div class="search-placeholder" id="guestsText">沟通记录</div>
          </div>
          <button class="search-button" id="searchButton">
              <svg class="search-icon" viewBox="0 0 32 32">
                  <path d="M13 24c6.075 0 11-4.925 11-11S19.075 2 13 2 2 6.925 2 13s4.925 11 11 11zm8-3l9 9-3 3-9-9v-2h2z"/>
              </svg>
              <span class="search-button-text">搜索</span>
          </button>
      </div>
      <div class="dropdown-container" id="dropdownContainer">
          <div class="dropdown-content" id="dropdownContent"></div>
      </div>
  </div>

  <script>
      class AirbnbSearchBox {
          constructor() {
              this.currentActiveType = null;
              this.guestCounts = { adults: 0, children: 0, infants: 0 };
              this.selectedLocation = '';
              this.initElements();
              this.bindEvents();
              this.setupGuestCounters();
          }

          initElements() {
              this.searchBar = document.getElementById('searchBar');
              this.activeIndicator = document.getElementById('activeIndicator');
              this.dropdownContainer = document.getElementById('dropdownContainer');
              this.dropdownContent = document.getElementById('dropdownContent');
              this.locationText = document.getElementById('locationText');
              this.guestsText = document.getElementById('guestsText');
              this.searchItems = document.querySelectorAll('.search-item');
              this.searchSeparator = document.querySelector('.search-separator');
              this.searchButton = document.getElementById('searchButton');
          }

          bindEvents() {
              this.searchItems.forEach(item => {
                  item.addEventListener('click', (e) => {
                      e.stopPropagation();
                      const type = item.dataset.type;
                      this.showDropdown(type, item);
                  });
                  item.addEventListener('mouseover', (e) => this.handleSearchItemHover(e.currentTarget, true));
                  item.addEventListener('mouseout', (e) => this.handleSearchItemHover(e.currentTarget, false));
              });

              document.addEventListener('click', (e) => {
                  if (!e.target.closest('.search-container')) {
                      this.hideDropdown();
                  }
              });

              document.getElementById('searchButton').addEventListener('click', () => {
                  this.performSearch();
              });

              this.activeIndicator.addEventListener('click', (e) => {
                  e.stopPropagation();
              });
          }

          showDropdown(type, activeItem) {
              if (this.currentActiveType === type) return;
              const isFirstDisplay = !this.dropdownContainer.classList.contains('show');
              const isMenuSizeChange = this.currentActiveType && this.currentActiveType !== type;
              const isPositionChange = this.currentActiveType && this.currentActiveType !== type;
              let flipData = null;
              if (isMenuSizeChange) {
                  const oldRect = this.dropdownContainer.getBoundingClientRect();
                  flipData = {
                      oldWidth: oldRect.width,
                      oldHeight: oldRect.height,
                      oldLeft: oldRect.left - this.searchBar.getBoundingClientRect().left,
                      oldTop: oldRect.top
                  };
              }
              this.currentActiveType = type;
              this.searchItems.forEach(item => item.classList.remove('active'));
              activeItem.classList.add('active');
              if (isFirstDisplay) {
                  this.activeIndicator.style.setProperty('transition', 'none', 'important');
                  this.dropdownContainer.style.transition = 'none';
                  this.activeIndicator.offsetHeight;
                  this.dropdownContainer.offsetHeight;
              } else if (isPositionChange) {
                  this.activeIndicator.style.setProperty('transition', getTransitionString(), 'important');
                  this.dropdownContainer.style.transition = 'none';
              }
              if (isMenuSizeChange && flipData) {
                  this.dropdownContainer.style.visibility = 'hidden';
                  this.updateDropdownContent(type);
                  this.dropdownContainer.style.width = (type === 'guests' ? 850 : 407) + 'px';
                  this.dropdownContainer.style.height = 'auto';
                  const newHeight = this.dropdownContainer.offsetHeight;
                  this.dropdownContainer.style.left = flipData.oldLeft + 'px';
                  this.dropdownContainer.style.width = flipData.oldWidth + 'px';
                  this.dropdownContainer.style.height = flipData.oldHeight + 'px';
                  this.dropdownContainer.style.transformOrigin = 'top left';
                  this.dropdownContainer.style.visibility = 'visible';
                  this.updateActiveIndicator(activeItem);
                  this.dropdownContainer.offsetHeight;
                  this.dropdownContainer.style.transition = getTransitionString();
                  requestAnimationFrame(() => {
                      this.dropdownContainer.style.width = (type === 'guests' ? 850 : 407) + 'px';
                      this.dropdownContainer.style.height = newHeight + 'px';
                  });
              } else {
                  this.updateDropdownContent(type);
                  this.positionDropdown(activeItem);
                  if (isFirstDisplay) {
                      this.dropdownContainer.style.opacity = '0.2';
                      this.dropdownContainer.style.transform = 'scale(0.5)';
                      this.dropdownContainer.style.transformOrigin = 'top center';
                      this.dropdownContainer.style.visibility = 'visible';
                      this.dropdownContainer.offsetHeight;
                      this.activeIndicator.offsetHeight;
                      this.dropdownContainer.style.transition = getTransitionString();
                      this.activeIndicator.style.removeProperty('transition');
                      requestAnimationFrame(() => {
                          this.dropdownContainer.style.opacity = '1';
                          this.dropdownContainer.style.transform = 'scale(1)';
                      });
                  } else if (isPositionChange) {
                      this.dropdownContainer.style.opacity = '1';
                      this.dropdownContainer.style.transform = 'scale(1)';
                      this.dropdownContainer.style.visibility = 'visible';
                      this.dropdownContainer.offsetHeight;
                      requestAnimationFrame(() => {
                          this.dropdownContainer.style.setProperty('transition', getTransitionString(), 'important');
                          this.activeIndicator.style.setProperty('transition', getTransitionString(), 'important');
                      });
                  } else {
                      this.dropdownContainer.offsetHeight;
                      this.activeIndicator.offsetHeight;
                  }
              }
              if (isFirstDisplay) {
                  this.updateActiveIndicator(activeItem);
                  requestAnimationFrame(() => {
                      this.activeIndicator.style.setProperty('transition', getTransitionString(), 'important');
                  });
              } else if (isPositionChange) {
                  this.updateActiveIndicator(activeItem);
                  setTimeout(() => {
                      this.activeIndicator.style.setProperty('transition', getTransitionString(), 'important');
                  }, 300);
              } else {
                  this.updateActiveIndicator(activeItem);
              }
              this.searchBar.classList.add('expanded');
              this.dropdownContainer.classList.add('show');
              this.activeIndicator.classList.add('show');
              this.searchButton.classList.add('expanded');
          }

          hideDropdown() {
              this.currentActiveType = null;
              this.searchBar.classList.remove('expanded');
              this.searchBar.classList.remove('hovering-item');
              this.dropdownContainer.style.setProperty('transition', 'none', 'important');
              this.activeIndicator.style.setProperty('transition', 'none', 'important');
              this.dropdownContainer.offsetHeight;
              this.dropdownContainer.style.visibility = 'hidden';
              this.dropdownContainer.style.opacity = '0';
              this.dropdownContainer.style.transform = 'scale(0.5)';
              this.dropdownContainer.style.width = '';
              this.dropdownContainer.style.height = '';
              this.dropdownContainer.style.left = '';
              this.dropdownContainer.style.transformOrigin = '';
              this.activeIndicator.style.left = '';
              this.activeIndicator.style.top = '';
              this.activeIndicator.style.width = '';
              this.activeIndicator.style.height = '';
              this.activeIndicator.style.transform = '';
              this.activeIndicator.style.transformOrigin = '';
              this.dropdownContainer.classList.remove('show');
              this.activeIndicator.classList.remove('show');
              this.searchItems.forEach(item => item.classList.remove('active'));
              this.searchButton.classList.remove('expanded');
              requestAnimationFrame(() => {
                  this.dropdownContainer.style.removeProperty('transition');
                  this.activeIndicator.style.removeProperty('transition');
              });
          }

          updateActiveIndicator(activeItem) {
              const itemRect = activeItem.getBoundingClientRect();
              const searchBarRect = this.searchBar.getBoundingClientRect();
              const computedStyle = getComputedStyle(this.searchBar);
              const borderLeftWidth = parseFloat(computedStyle.borderLeftWidth);
              const borderTopWidth = parseFloat(computedStyle.borderTopWidth);
              const borderRightWidth = parseFloat(computedStyle.borderRightWidth);
              const left = (itemRect.left - searchBarRect.left) - borderLeftWidth;
              const top = (itemRect.top - searchBarRect.top) - borderTopWidth;
              let width = itemRect.width;
              if (activeItem.dataset.type === 'guests') {
                  width = (searchBarRect.width - borderLeftWidth - borderRightWidth) - left;
              }
              this.activeIndicator.style.left = left + 'px';
              this.activeIndicator.style.width = width + 'px';
              this.activeIndicator.style.height = '100%';
              this.activeIndicator.style.top = top + 'px';
          }

          positionDropdown(activeItem) {
              const searchBarRect = this.searchBar.getBoundingClientRect();
              const itemRect = activeItem.getBoundingClientRect();
              const type = activeItem.dataset.type;
              const containerWidth = type === 'guests' ? 850 : 407;
              if (type === 'location') {
                  const activeItemCenter = (itemRect.left - searchBarRect.left) + (itemRect.width / 2);
                  let left = activeItemCenter - (containerWidth / 2);
                  const maxLeft = searchBarRect.width - containerWidth;
                  left = Math.max(0, Math.min(left, maxLeft));
                  this.dropdownContainer.style.left = left + 'px';
                  this.dropdownContainer.style.transformOrigin = '50% top';
              } else {
                  this.dropdownContainer.style.left = '0px';
                  this.dropdownContainer.style.transformOrigin = 'top left';
              }
              this.dropdownContainer.style.width = containerWidth + 'px';
          }

          updateDropdownContent(type) {
              if (type === 'location') {
                  this.dropdownContent.innerHTML = this.getLocationContent();
                  this.bindLocationEvents();
              } else if (type === 'guests') {
                  this.dropdownContent.innerHTML = this.getTimePickerContent();
                  this.bindGuestEvents();
              }
          }

          getTimePickerContent() {
              // 动态生成2025年8月往前12个月的月份卡片
              const now = new Date(2025, 7); // 固定到2025年8月
              let year = 2025, month = 8;
              let months = [];
              for (let i = 0; i < 12; i++) {
                  let m = month - i;
                  let y = year;
                  if (m < 1) {
                      y -= Math.ceil((1 - m) / 12);
                      m = ((m - 1 + 12 * 2) % 12) + 1;
                  }
                  months.push({ y, m }); // 从最新到最旧
              }
              const monthIconSVG = `<svg class="month-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" aria-hidden="true" role="presentation" focusable="false" style="display: block; height: 32px; width: 32px; fill: #6a6a6a;"><path d="M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v21.81a5 5 0 0 1-4.78 5H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h18.84a3 3 0 0 0 3-2.82v-.18zm-18.66-8h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z"></path></svg>`;
              const monthsCardsHTML = months.map(({ y, m }, index) => {
                  const isActive = y === 2025 && m === 8 ? 'active' : '';
                  const iconPath = isActive ? '<path d="M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v16.07a2 2 0 0 1-.46 1.28l-.12.13L21 29.75a2 2 0 0 1-1.24.58H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h11.18v-5.66a5 5 0 0 1 4.78-5h5.88zm-.08 8h-5.58a3 3 0 0 0-3 2.82v5.76zm-18.58-16h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z"></path>' : '<path d="M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v21.81a5 5 0 0 1-4.78 5H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h18.84a3 3 0 0 0 3-2.82v-.18zm-18.66-8h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z"></path>';
                  return `<div class="month-card ${isActive}">${monthIconSVG.replace('<path d="', iconPath)}<div class="month-name">${m} 月</div><div class="month-year">${y}</div></div>`;
              }).join('');
              return `
                  <div class="container" style="max-width: none; padding: 0; margin: 0;">
                      <div class="dropdown" style="border: none; box-shadow: none; border-radius: 0;">
                          <div class="dropdown-header">
                              <div class="tabs-container">
                                  <button class="tab active" id="tab-projects">WorkFeed</button>
                                  <button class="tab" id="tab-timeline">最近沟通</button>
                              </div>
                          </div>
                          <div class="dropdown-content">
                              <div class="projects-view" id="projects-view">
                                  <h3>选择相关WorkFeed</h3>
                                  <div class="projects-grid">
                                      <div class="project-card"><div class="project-name">项目名称1</div><div class="project-date">8月7日</div></div>
                                      <div class="project-card"><div class="project-name">项目名称1</div><div class="project-date">8月7日</div></div>
                                      <div class="project-card"><div class="project-name">项目名称1</div><div class="project-date">8月7日</div></div>
                                      <div class="project-card"><div class="project-name">项目名称1</div><div class="project-date">8月8日</div></div>
                                      <div class="project-card"><div class="project-name">项目名称1</div><div class="project-date">8月8日</div></div>
                                      <div class="project-card"><div class="project-name">项目名称1</div><div class="project-date">8月8日</div></div>
                                  </div>
                              </div>
                              <div class="timeline-view hidden" id="timeline-view">
                                  <h3>您多久前沟通的？</h3>
                                  <div class="time-options">
                                      <button class="time-option">本周</button>
                                      <button class="time-option active">上月</button>
                                      <button class="time-option">近两周</button>
                                  </div>
                                  <div class="timeline-question">您上次是几月沟通的？</div>
                                  <div class="months-container">
                                      <button class="nav-button" id="prev-months"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" aria-hidden="true" role="presentation" focusable="false" style="display: block; fill: none; height: 12px; width: 12px; stroke: currentcolor; stroke-width: 5.33333; overflow: visible;"><path fill="none" d="M20 28 8.7 16.7a1 1 0 0 1 0-1.4L20 4"></path></svg></button>
                                      <div class="months-grid" id="months-grid">
                                          ${monthsCardsHTML}
                                      </div>
                                      <button class="nav-button" id="next-months"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" aria-hidden="true" role="presentation" focusable="false" style="display: block; fill: none; height: 12px; width: 12px; stroke: currentcolor; stroke-width: 5.33333; overflow: visible;"><path fill="none" d="m12 4 11.3 11.3a1 1 0 0 1 0 1.4L12 28"></path></svg></button>
                                  </div>
                              </div>
                          </div>
                      </div>
                  </div>
              `;
          }

          getLocationContent() {
              const locations = [
                  { name: '北京', desc: '中国首都，历史文化名城' },
                  { name: '上海', desc: '国际大都市，东方明珠' },
                  { name: '南京', desc: '六朝古都，文化名城' },
                  { name: '广州', desc: '千年商都，美食天堂' },
                  { name: '深圳', desc: '创新之都，活力四射' },
                  { name: '杭州', desc: '人间天堂，西湖美景' },
                  { name: '成都', desc: '天府之国，悠闲之都' },
                  { name: '西安', desc: '古都明珠，华夏源脉' },
                  { name: '苏州', desc: '园林之城，东方威尼斯' },
                  { name: '厦门', desc: '海上花园，温馨之城' }
              ];
              // To make it scrollable for demonstration
              const repeatedLocations = Array(3).fill(locations).flat();

              return `
                  <div class="scrollable-content">
                      <div class="dropdown-section">
                          <div class="dropdown-title">热门目的地</div>
                          ${repeatedLocations.map(location => `
                              <div class="location-item" data-location="${location.name}">
                                  <div class="location-name">${location.name}</div>
                                  <div class="location-desc">${location.desc}</div>
                              </div>
                          `).join('')}
                      </div>
                  </div>
                  <div class="custom-scrollbar">
                      <div class="custom-scrollbar-thumb"></div>
                  </div>
              `;
          }

          bindLocationEvents() {
              const locationItems = this.dropdownContent.querySelectorAll('.location-item');
              locationItems.forEach(item => {
                  item.addEventListener('click', () => {
                      const location = item.dataset.location;
                      this.selectedLocation = location;
                      this.locationText.textContent = location;
                      this.hideDropdown();
                  });
              });
              this.setupCustomScrollbar();
          }

          setupCustomScrollbar() {
              const scrollableContent = this.dropdownContent.querySelector('.scrollable-content');
              const customScrollbar = this.dropdownContent.querySelector('.custom-scrollbar');
              const thumb = this.dropdownContent.querySelector('.custom-scrollbar-thumb');

              if (!scrollableContent || !customScrollbar || !thumb) return;

              const setup = () => {
                  const scrollHeight = scrollableContent.scrollHeight;
                  const clientHeight = scrollableContent.clientHeight;

                  if (scrollHeight > clientHeight) {
                      customScrollbar.style.display = 'block';
                      this.dropdownContent.classList.add('has-custom-scrollbar');

                      const trackHeight = customScrollbar.clientHeight;
                      const thumbHeight = Math.max(20, (clientHeight / scrollHeight) * trackHeight);
                      thumb.style.height = `${thumbHeight}px`;

                      const updateThumbPosition = () => {
                          const scrollTop = scrollableContent.scrollTop;
                          const maxScrollTop = scrollHeight - clientHeight;
                          const maxThumbTop = trackHeight - thumbHeight;
                          const thumbTop = maxScrollTop > 0 ? (scrollTop / maxScrollTop) * maxThumbTop : 0;
                          thumb.style.top = `${thumbTop}px`;
                      };

                      scrollableContent.addEventListener('scroll', updateThumbPosition);

                      let isDragging = false;
                      let startY;
                      let startScrollTop;

                      const onMouseDown = (e) => {
                          isDragging = true;
                          startY = e.clientY;
                          startScrollTop = scrollableContent.scrollTop;
                          document.body.style.userSelect = 'none';
                          document.addEventListener('mousemove', onMouseMove);
                          document.addEventListener('mouseup', onMouseUp);
                          e.preventDefault();
                      };
                      
                      const onMouseMove = (e) => {
                          if (!isDragging) return;
                          const deltaY = e.clientY - startY;
                          const trackHeight = customScrollbar.clientHeight;
                          const thumbHeight = thumb.clientHeight;
                          const maxThumbTop = trackHeight - thumbHeight;
                          const scrollableRatio = (scrollableContent.scrollHeight - scrollableContent.clientHeight) / maxThumbTop;
                          
                          scrollableContent.scrollTop = startScrollTop + deltaY * scrollableRatio;
                      };

                      const onMouseUp = () => {
                          isDragging = false;
                          document.body.style.userSelect = '';
                          document.removeEventListener('mousemove', onMouseMove);
                          document.removeEventListener('mouseup', onMouseUp);
                      };

                      thumb.addEventListener('mousedown', onMouseDown);

                      updateThumbPosition();
                  } else {
                      customScrollbar.style.display = 'none';
                      this.dropdownContent.classList.remove('has-custom-scrollbar');
                  }
              };
              
              // Use a small timeout to ensure layout is calculated
              setTimeout(setup, 50);
          }

          bindGuestEvents() {
              const tabProjects = document.getElementById('tab-projects');
              const tabTimeline = document.getElementById('tab-timeline');
              const projectsView = document.getElementById('projects-view');
              const timelineView = document.getElementById('timeline-view');
              const projectCards = document.querySelectorAll('.project-card');
              const timeOptions = document.querySelectorAll('.time-option');
              const prevMonthsButton = document.getElementById('prev-months');
              const nextMonthsButton = document.getElementById('next-months');
              const monthsGrid = document.getElementById('months-grid');

              function updateNavButtonsVisibility() {
                  if (!monthsGrid || monthsGrid.clientWidth === 0) {
                      requestAnimationFrame(updateNavButtonsVisibility);
                      return;
                  }
                  const currentTransform = parseFloat(getComputedStyle(monthsGrid).transform.split(',')[4]) || 0;
                  const scrollWidth = monthsGrid.scrollWidth;
                  const clientWidth = monthsGrid.clientWidth;
                  const tolerance = 1;
                  const maxTranslate = 0; // 最右侧
                  const minTranslate = -(scrollWidth - clientWidth); // 最左侧
                  prevMonthsButton.classList.toggle('hidden', currentTransform >= maxTranslate - tolerance);
                  nextMonthsButton.classList.toggle('hidden', currentTransform <= minTranslate + tolerance);
              }

              function smoothScrollMonths(direction) {
                  const cardWidth = 130; // 卡片宽度120px + 间距10px
                  const cardsPerView = Math.floor(monthsGrid.clientWidth / cardWidth);
                  const slideWidth = cardsPerView * cardWidth;
                  const currentTransform = parseFloat(getComputedStyle(monthsGrid).transform.split(',')[4]) || 0;
                  const maxTranslate = 0;
                  const minTranslate = -(monthsGrid.scrollWidth - monthsGrid.clientWidth);
                  let targetTranslate = direction === 'next' ? currentTransform - slideWidth : currentTransform + slideWidth;
                  targetTranslate = Math.min(maxTranslate, Math.max(minTranslate, targetTranslate));
                  monthsGrid.style.transform = `translateX(${targetTranslate}px)`;
              }

              if (monthsGrid) {
                  monthsGrid.style.transform = 'translateX(0)'; // 默认显示最新月份
                  setTimeout(() => {
                      updateNavButtonsVisibility();
                  }, 0);
                  monthsGrid.addEventListener('transitionend', updateNavButtonsVisibility);
              }

              const defaultIconPath = '<path d="M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v21.81a5 5 0 0 1-4.78 5H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h18.84a3 3 0 0 0 3-2.82v-.18zm-18.66-8h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z"></path>';
              const selectedIconPath = '<path d="M11.67 0v1.67h8.66V0h2v1.67h6a2 2 0 0 1 2 1.85v16.07a2 2 0 0 1-.46 1.28l-.12.13L21 29.75a2 2 0 0 1-1.24.58H6.67a5 5 0 0 1-5-4.78V3.67a2 2 0 0 1 1.85-2h6.15V0zm16.66 11.67H3.67v13.66a3 3 0 0 0 2.82 3h11.18v-5.66a5 5 0 0 1 4.78-5h5.88zm-.08 8h-5.58a3 3 0 0 0-3 2.82v5.76zm-18.58-16h-6v6h24.66v-6h-6v1.66h-2V3.67h-8.66v1.66h-2z"></path>';

              function getMonthCards() {
                  return monthsGrid ? monthsGrid.querySelectorAll('.month-card') : [];
              }

              const switchTab = (targetTab, targetView, otherView) => {
                  if (targetTab.classList.contains('active')) return;
                  const dropdownContent = document.querySelector('.dropdown-content');
                  const currentHeight = dropdownContent.offsetHeight;
                  document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
                  targetTab.classList.add('active');
                  otherView.classList.add('hidden');
                  targetView.classList.remove('hidden');
                  const newHeight = dropdownContent.offsetHeight;
                  if (currentHeight === newHeight) return;
                  dropdownContent.style.height = currentHeight + 'px';
                  dropdownContent.style.transition = 'height 0.4s cubic-bezier(0, 0, 0.2, 1)';
                  dropdownContent.offsetHeight;
                  requestAnimationFrame(() => {
                      dropdownContent.style.height = newHeight + 'px';
                      setTimeout(() => {
                          dropdownContent.style.height = '';
                          dropdownContent.style.transition = '';
                      }, 300);
                  });
                  if (targetView === timelineView && monthsGrid) {
                      monthsGrid.style.transform = 'translateX(0)';
                      setTimeout(() => {
                          updateNavButtonsVisibility();
                      }, 0);
                  }
              };

              tabProjects.addEventListener('click', function() {
                  switchTab(this, projectsView, timelineView);
              });
              tabTimeline.addEventListener('click', function() {
                  switchTab(this, timelineView, projectsView);
              });

              function clearTimeOptions() { timeOptions.forEach(opt => opt.classList.remove('active')); }
              function clearMonthCards() {
                  getMonthCards().forEach(c => {
                      c.classList.remove('active');
                      const icon = c.querySelector('.month-icon');
                      if (icon) { icon.innerHTML = defaultIconPath; icon.style.fill = '#6a6a6a'; }
                  });
              }
              function clearProjectCards() { projectCards.forEach(p => p.classList.remove('active')); }

              timeOptions.forEach(option => {
                  option.addEventListener('click', function() {
                      clearTimeOptions();
                      clearMonthCards();
                      clearProjectCards();
                      this.classList.add('active');
                  });
              });

              getMonthCards().forEach(card => {
                  card.addEventListener('click', function() {
                      clearTimeOptions();
                      clearProjectCards();
                      clearMonthCards();
                      this.classList.add('active');
                      const icon = this.querySelector('.month-icon');
                      if (icon) { icon.innerHTML = selectedIconPath; icon.style.fill = '#000'; }
                  });
              });

              projectCards.forEach(card => {
                  card.addEventListener('click', function() {
                      clearTimeOptions();
                      clearMonthCards();
                      clearProjectCards();
                      this.classList.add('active');
                  });
              });

              prevMonthsButton.addEventListener('click', () => {
                  console.log('Prev button clicked');
                  smoothScrollMonths('prev');
              });
              nextMonthsButton.addEventListener('click', () => {
                  console.log('Next button clicked');
                  smoothScrollMonths('next');
              });

              // 键盘可访问性
              monthsGrid.addEventListener('keydown', (e) => {
                  if (e.key === 'ArrowLeft') {
                      smoothScrollMonths('prev');
                      e.preventDefault();
                  } else if (e.key === 'ArrowRight') {
                      smoothScrollMonths('next');
                      e.preventDefault();
                  }
              });
          }

          changeGuestCount(type, change) {
              const newCount = Math.max(0, this.guestCounts[type] + change);
              this.guestCounts[type] = newCount;
              const el = document.getElementById(type + 'Count');
              if (el) el.textContent = newCount;
              this.updateGuestsText();
              this.updateCounterStates();
          }

          updateCounterStates() {
              Object.keys(this.guestCounts).forEach(type => {
                  const minusBtn = this.dropdownContent.querySelector(`[data-type="${type}"][data-action="minus"]`);
                  if (minusBtn) { minusBtn.disabled = this.guestCounts[type] === 0; }
              });
          }

          updateGuestsText() {
              const total = Object.values(this.guestCounts).reduce((sum, count) => sum + count, 0);
              if (total === 0) { this.guestsText.textContent = '沟通记录'; }
              else { this.guestsText.textContent = `${total}位房客`; }
          }

          setupGuestCounters() { this.updateGuestsText(); }

          performSearch() {
              const total = Object.values(this.guestCounts).reduce((sum, count) => sum + count, 0);
              if (!this.selectedLocation && total === 0) {
                  alert('请选择目的地或添加房客！');
                  return;
              }
              console.log('执行搜索:', { location: this.selectedLocation || '未选择', guests: this.guestCounts, total: total });
              alert(`搜索条件：\n目的地: ${this.selectedLocation || '未选择'}\n房客总数: ${total}人\n成人: ${this.guestCounts.adults}人\n儿童: ${this.guestCounts.children}人\n婴幼儿: ${this.guestCounts.infants}人`);
          }

          handleSearchItemHover(hoveredItem, isHovering) {
              if (!this.searchBar.classList.contains('expanded')) {
                  if (isHovering) { this.searchBar.classList.add('hovering-item'); }
                  else { this.searchBar.classList.remove('hovering-item'); }
              }
          }
      }

      document.addEventListener('DOMContentLoaded', () => { new AirbnbSearchBox(); });
  </script>
</body>
</html>